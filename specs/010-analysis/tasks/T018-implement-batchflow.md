---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement BatchFlow"
labels: feature
---

**Description**

Implement the `BatchFlow` class in `lib/src/batch_flow.dart`. This class is for orchestrating flows that run over a batch of inputs.

**Requirements**

- [ ] Create the `lib/src/batch_flow.dart` file.
- [ ] Implement the `BatchFlow` class, which inherits from `Flow`.
