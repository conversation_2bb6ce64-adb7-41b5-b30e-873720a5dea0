---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement AsyncBatchFlow"
labels: feature
---

**Description**

Implement the `AsyncBatchFlow` class in `lib/src/async_batch_flow.dart`. This class is for orchestrating asynchronous batch flows.

**Requirements**

- [ ] Create the `lib/src/async_batch_flow.dart` file.
- [ ] Implement the `AsyncBatchFlow` class, which inherits from `AsyncFlow`.
