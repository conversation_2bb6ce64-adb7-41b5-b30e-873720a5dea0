---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement AsyncNode"
labels: feature
---

**Description**

Implement the `AsyncNode` class in `lib/src/async_node.dart`. This class is for defining nodes with `async`/`await` native methods.

**Requirements**

- [ ] Create the `lib/src/async_node.dart` file.
- [ ] Implement the `AsyncNode` class, which inherits from `Node`.
