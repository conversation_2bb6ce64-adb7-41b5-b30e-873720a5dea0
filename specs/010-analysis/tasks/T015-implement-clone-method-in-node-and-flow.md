---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement clone() method in Node and Flow"
labels: feature
---

**Description**

Implement the `clone()` method in `lib/src/node.dart` and `lib/src/flow.dart`. This method is crucial for isolating the execution state of nodes within a `Flow`.

**Requirements**

- [ ] Implement the `clone()` method in `lib/src/node.dart`.
- [ ] Implement the `clone()` method in `lib/src/flow.dart`.
- [ ] The methods should return new instances of `Node` and `Flow` with the same properties as the original.
