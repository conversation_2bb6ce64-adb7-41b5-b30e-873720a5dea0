---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Create failing tests for BatchFlow"
labels: test
---

**Description**

Create failing tests for the `BatchFlow` class. These tests should cover the basic functionality of the `BatchFlow`, which is to orchestrate a flow that runs over a batch of inputs.

**Requirements**

- [ ] Create a new test file `test/src/batch_flow_test.dart`.
- [ ] The tests should fail because the `BatchFlow` class is not implemented yet.
