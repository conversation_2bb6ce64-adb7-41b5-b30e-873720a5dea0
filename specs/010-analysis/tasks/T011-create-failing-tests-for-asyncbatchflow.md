---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Create failing tests for AsyncBatchFlow"
labels: test
---

**Description**

Create failing tests for the `AsyncBatchFlow` class. These tests should cover the basic functionality of the `AsyncBatchFlow`, which is for orchestrating asynchronous batch flows.

**Requirements**

- [ ] Create a new test file `test/src/async_batch_flow_test.dart`.
- [ ] The tests should fail because the `AsyncBatchFlow` class is not implemented yet.
