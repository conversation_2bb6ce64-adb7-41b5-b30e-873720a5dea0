---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement operator overloading"
labels: feature
---

**Description**

Implement operator overloading for `>>` and `-` in `lib/src/base_node.dart`. This will provide a more fluent API for defining flows.

**Requirements**

- [ ] Implement the `>>` operator to chain nodes.
- [ ] Implement the `-` operator to define conditional transitions.
