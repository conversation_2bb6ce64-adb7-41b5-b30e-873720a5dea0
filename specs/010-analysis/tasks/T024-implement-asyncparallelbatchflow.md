---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Implement AsyncParallelBatchFlow"
labels: feature
---

**Description**

Implement the `AsyncParallelBatchFlow` class in `lib/src/async_parallel_batch_flow.dart`. This class is for orchestrating parallel, asynchronous batch flows.

**Requirements**

- [ ] Create the `lib/src/async_parallel_batch_flow.dart` file.
- [ ] Implement the `AsyncParallelBatchFlow` class, which inherits from `AsyncFlow`.
