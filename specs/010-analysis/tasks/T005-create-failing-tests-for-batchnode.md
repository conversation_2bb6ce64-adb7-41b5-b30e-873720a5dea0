---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Create failing tests for BatchNode"
labels: test
---

**Description**

Create failing tests for the `BatchNode` class. These tests should cover the basic functionality of the `BatchNode`, which is to process a batch of items.

**Requirements**

- [ ] Create a new test file `test/src/batch_node_test.dart`.
- [ ] The tests should fail because the `BatchNode` class is not implemented yet.
