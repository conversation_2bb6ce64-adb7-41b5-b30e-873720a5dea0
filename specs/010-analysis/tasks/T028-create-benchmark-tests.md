---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Create benchmark tests"
labels: test
---

**Description**

Create benchmark tests in the `test/benchmark/` directory. These tests will be used to compare the performance of the Dart implementation to the Python implementation.

**Requirements**

- [ ] Create a new directory `test/benchmark/`.
- [ ] Create benchmark tests for the core features of the library, including:
    - Basic node execution
    - Flow orchestration
    - Batch processing
    - Asynchronous execution
