---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Write tests for Node"
labels: test
---

**Description**

Write tests for the `Node` class in `test/src/node_test.dart`. The tests should be ported from the relevant sections of `third_party/PocketFlow-Python/tests/test_flow_basic.py` and `third_party/PocketFlow-Python/tests/test_async_flow.py`.

**Requirements**

- [ ] Tests for `Node` are implemented in `test/src/node_test.dart`.
- [ ] The tests should fail before the implementation is complete.
- [ ] There is no drop in test coverage.
