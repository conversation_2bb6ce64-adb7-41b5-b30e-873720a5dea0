---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Write tests for BaseNode"
labels: test
---

**Description**

Write tests for the `BaseNode` class in `test/src/base_node_test.dart`. The tests should be ported from the relevant sections of `third_party/PocketFlow-Python/tests/test_flow_basic.py`.

**Requirements**

- [ ] Tests for `BaseNode` are implemented in `test/src/base_node_test.dart`.
- [ ] The tests should fail before the implementation is complete.
- [ ] There is no drop in test coverage.
