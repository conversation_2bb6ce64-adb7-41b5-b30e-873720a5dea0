---
name: Test
about: Adding missing tests or correcting existing tests
title: "test: Write tests for Flow"
labels: test
---

**Description**

Write tests for the `Flow` class in `test/src/flow_test.dart`. The tests should be ported from the relevant sections of `third_party/PocketFlow-Python/tests/test_flow_basic.py`.

**Requirements**

- [ ] Tests for `Flow` are implemented in `test/src/flow_test.dart`.
- [ ] The tests should fail before the implementation is complete.
- [ ] There is no drop in test coverage.
