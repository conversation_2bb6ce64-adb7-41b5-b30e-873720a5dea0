---
name: Feature Request
about: A new feature to be added to the project
title: "feat: Project Setup"
labels: feature
---

**Description**

Setup the initial project structure for the PocketFlow Dart port.

**Requirements**

- [ ] T001 [P] Create the directory structure in `lib/` and `test/` as defined in the implementation plan.
- [ ] T002 [P] Create empty files: `lib/pocketflow.dart`, `lib/src/base_node.dart`, `lib/src/node.dart`, `lib/src/flow.dart`.
- [ ] T003 [P] Create empty test files: `test/src/base_node_test.dart`, `test/src/node_test.dart`, `test/src/flow_test.dart`.
