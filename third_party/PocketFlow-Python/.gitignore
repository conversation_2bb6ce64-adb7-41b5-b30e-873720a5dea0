# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db


# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
ENV/

# Logs and databases
*.log
*.sql
*.sqlite

# Build output
dist/
build/
out/

# Coverage reports
coverage/
.coverage
.coverage.*
htmlcov/

# Misc
*.bak
*.tmp
*.temp


test.ipynb
.pytest_cache/
cookbook/pocketflow-multi-agent/.python-version


# local
uv.lock
.python-version
pyproject.toml
usage.md
cookbook/pocketflow-minimal-example/viz/flow_visualization.html
cookbook/pocketflow-minimal-example/viz/flow_visualization.json
.claude/
