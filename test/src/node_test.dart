import 'package:pocketflow/pocketflow.dart';
import 'package:test/test.dart';

// --- Node Definitions from test_flow_basic.py ---

class NumberNode extends Node {
  NumberNode(this.number);
  final int number;

  @override
  Future<void> prep(Map<String, dynamic> sharedStorage) async {
    sharedStorage['current'] = number;
  }

  @override
  BaseNode createInstance() {
    return NumberNode(number);
  }

  @override
  NumberNode clone() {
    return super.clone() as NumberNode;
  }
}

class AddNode extends Node {
  AddNode(this.number);
  final int number;

  @override
  Future<void> prep(Map<String, dynamic> sharedStorage) async {
    sharedStorage['current'] = (sharedStorage['current'] as int? ?? 0) + number;
  }

  @override
  BaseNode createInstance() {
    return AddNode(number);
  }

  @override
  AddNode clone() {
    return super.clone() as AddNode;
  }
}

class MultiplyNode extends Node {
  MultiplyNode(this.number);
  final int number;

  @override
  Future<void> prep(Map<String, dynamic> sharedStorage) async {
    sharedStorage['current'] = (sharedStorage['current'] as int? ?? 0) * number;
  }

  @override
  Node clone() {
    return MultiplyNode(number)..params = Map.from(params);
  }
}

// A test node that can be configured to fail.
class FallibleNode extends Node {
  FallibleNode({
    this.failCount = 0,
    this.successValue = 'success',
    this.fallbackValue = 'fallback',
    this.useCustomFallback = false,
    this.rethrowNonException = false,
    super.maxRetries,
    super.wait,
  });
  int attempts = 0;
  final int failCount;
  final dynamic successValue;
  final dynamic fallbackValue;
  final bool useCustomFallback;
  final bool rethrowNonException;

  @override
  Future<dynamic> exec(dynamic prepResult) async {
    attempts++;
    if (attempts <= failCount) {
      if (rethrowNonException) {
        throw ArgumentError('a non-exception error');
      }
      throw Exception('Failed on attempt $attempts');
    }
    return successValue;
  }

  @override
  Future<dynamic> execFallback(dynamic prepResult, Exception error) async {
    if (useCustomFallback) {
      return fallbackValue;
    }
    return super.execFallback(prepResult, error);
  }

  @override
  Future<dynamic> post(
    Map<String, dynamic> shared,
    dynamic prepResult,
    dynamic execResult,
  ) async {
    // Return the execResult so it can be asserted in tests.
    return execResult;
  }

  @override
  Node clone() {
    return FallibleNode(
      failCount: failCount,
      successValue: successValue,
      fallbackValue: fallbackValue,
      useCustomFallback: useCustomFallback,
      rethrowNonException: rethrowNonException,
      maxRetries: maxRetries,
      wait: wait,
    )..params = Map.from(params);
  }
}

void main() {
  group('Node', () {
    test('NumberNode sets the initial value correctly', () async {
      final node = NumberNode(42);
      final storage = <String, dynamic>{};
      await node.run(storage);
      expect(storage['current'], 42);
    });

    test('AddNode adds to the value correctly', () async {
      final node = AddNode(10);
      final storage = <String, dynamic>{'current': 5};
      await node.run(storage);
      expect(storage['current'], 15);
    });

    test('MultiplyNode multiplies the value correctly', () async {
      final node = MultiplyNode(3);
      final storage = <String, dynamic>{'current': 5};
      await node.run(storage);
      expect(storage['current'], 15);
    });

    test(
      'AddNode should start from 0 if "current" is not in storage',
      () async {
        final node = AddNode(10);
        final storage = <String, dynamic>{}; // 'current' is missing
        await node.run(storage);
        expect(storage['current'], 10);
      },
    );

    test(
      'MultiplyNode should start from 0 if "current" is not in storage',
      () async {
        final node = MultiplyNode(3);
        final storage = <String, dynamic>{}; // 'current' is missing
        await node.run(storage);
        expect(storage['current'], 0);
      },
    );

    test('default exec implementation should return null', () async {
      final node = Node();
      final result = await node.exec(null);
      expect(result, isNull);
    });
  });

  group('Node retry and fallback', () {
    late Map<String, dynamic> sharedStorage;

    setUp(() {
      sharedStorage = {};
    });

    test('should succeed on the first attempt if failCount is 0', () async {
      final node = FallibleNode(maxRetries: 3);
      final result = await node.run(sharedStorage);
      expect(result, 'success');
      expect(node.attempts, 1);
    });

    test('should succeed on retry if failCount is within maxRetries', () async {
      final node = FallibleNode(failCount: 2, maxRetries: 3);
      final result = await node.run(sharedStorage);
      expect(result, 'success');
      expect(node.attempts, 3);
    });

    test(
      '''
should rethrow the exception if retries are exhausted with default fallback''',
      () async {
        final node = FallibleNode(failCount: 3, maxRetries: 2);
        await expectLater(
          () => node.run(sharedStorage),
          throwsA(isA<Exception>()),
        );
      },
    );

    test('should call custom fallback when retries are exhausted', () async {
      final node = FallibleNode(
        failCount: 3,
        maxRetries: 2,
        useCustomFallback: true,
      );
      final result = await node.run(sharedStorage);
      expect(result, 'fallback');
    });

    test('should wait between retries', () async {
      const waitDuration = Duration(milliseconds: 50);
      final node = FallibleNode(
        failCount: 1,
        maxRetries: 2,
        wait: waitDuration,
      );
      final stopwatch = Stopwatch()..start();
      await node.run(sharedStorage);
      stopwatch.stop();
      expect(stopwatch.elapsed, greaterThanOrEqualTo(waitDuration));
    });

    test('should rethrow non-Exception errors immediately', () async {
      final node = FallibleNode(
        failCount: 1,
        maxRetries: 3,
        rethrowNonException: true,
      );
      await expectLater(
        () => node.run(sharedStorage),
        throwsA(isA<ArgumentError>()),
      );
      // Fallback should not be reached, so attempts should be 1
      expect(node.attempts, 1);
    });
  });

  group('Node.clone()', () {
    test('should create a new instance with a deep copy of params', () {
      final original = Node(maxRetries: 3, wait: const Duration(seconds: 1));
      original.params['key'] = 'value';

      final cloned = original.clone();

      // Should be a new instance
      expect(identical(original, cloned), isFalse);

      // Properties should be the same
      expect(cloned.maxRetries, original.maxRetries);
      expect(cloned.wait, original.wait);

      // Params should be a copy, not a reference
      expect(cloned.params, equals(original.params));
      expect(identical(cloned.params, original.params), isFalse);

      // Modifying the cloned params should not affect the original
      cloned.params['key'] = 'newValue';
      expect(original.params['key'], 'value');
    });
  });
}
