<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/batch_node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - batch_node.dart<span style="font-size: 80%;"> (source / <a href="batch_node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">90.9&nbsp;%</td>
            <td class="headerCovTableEntry">11</td>
            <td class="headerCovTableEntry">10</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:pocketflow/src/node.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// An abstract class for processing a batch of items of type `I` and returning</span>
<span id="L4"><span class="lineNum">       4</span>              : /// a batch of items of type `O`.</span>
<span id="L5"><span class="lineNum">       5</span>              : ///</span>
<span id="L6"><span class="lineNum">       6</span>              : /// This node is useful for performing the same operation on a list of items.</span>
<span id="L7"><span class="lineNum">       7</span>              : /// The `prep` method extracts the list of items from the node's parameters,</span>
<span id="L8"><span class="lineNum">       8</span>              : /// and the `exec` method processes the list.</span>
<span id="L9"><span class="lineNum">       9</span>              : abstract class BatchNode&lt;I, O&gt; extends Node {</span>
<span id="L10"><span class="lineNum">      10</span>              :   /// The main execution logic for processing a batch of items.</span>
<span id="L11"><span class="lineNum">      11</span>              :   ///</span>
<span id="L12"><span class="lineNum">      12</span>              :   /// This method is intended to be implemented by subclasses to define the</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// specific processing logic for the batch. The [items] parameter is a list</span>
<span id="L14"><span class="lineNum">      14</span>              :   /// of items to be processed, and the method should return a list of processed</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// items.</span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC tlaBgUNC">           0 :   @override</span></span>
<span id="L17"><span class="lineNum">      17</span>              :   Future&lt;List&lt;O&gt;&gt; exec(covariant List&lt;I&gt; items);</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC tlaBgGNC">           1 :   @override</span></span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Prepares the batch of items for processing.</span>
<span id="L21"><span class="lineNum">      21</span>              :   ///</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// This method retrieves the list of items from the node's parameters. It</span>
<span id="L23"><span class="lineNum">      23</span>              :   /// expects a parameter named &quot;items&quot; which should be a `List&lt;I&gt;`.</span>
<span id="L24"><span class="lineNum">      24</span>              :   ///</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// Throws an [ArgumentError] if the &quot;items&quot; parameter is not provided or is</span>
<span id="L26"><span class="lineNum">      26</span>              :   /// of the wrong type.</span>
<span id="L27"><span class="lineNum">      27</span>              :   Future&lt;List&lt;I&gt;&gt; prep(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           2 :     final items = params['items'];</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              :     if (items == null) {</span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           1 :       throw ArgumentError('The &quot;items&quot; parameter must be provided.');</span></span>
<span id="L32"><span class="lineNum">      32</span>              :     }</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :     if (items is List&lt;I&gt;) {</span></span>
<span id="L35"><span class="lineNum">      35</span>              :       return items;</span>
<span id="L36"><span class="lineNum">      36</span>              :     }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           1 :     if (items is List) {</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           3 :       if (items.every((item) =&gt; item is I)) {</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :         return items.cast&lt;I&gt;();</span></span>
<span id="L41"><span class="lineNum">      41</span>              :       } else {</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           2 :         throw ArgumentError(</span></span>
<span id="L43"><span class="lineNum">      43</span>              :           'The &quot;items&quot; parameter must be a List where all elements are of '</span>
<span id="L44"><span class="lineNum">      44</span>              :           'type $I.',</span>
<span id="L45"><span class="lineNum">      45</span>              :         );</span>
<span id="L46"><span class="lineNum">      46</span>              :       }</span>
<span id="L47"><span class="lineNum">      47</span>              :     }</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :     throw ArgumentError(</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           2 :       'The &quot;items&quot; parameter must be a List, but got ${items.runtimeType}.',</span></span>
<span id="L51"><span class="lineNum">      51</span>              :     );</span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              :   @override</span>
<span id="L55"><span class="lineNum">      55</span>              :   /// Creates a copy of this [BatchNode].</span>
<span id="L56"><span class="lineNum">      56</span>              :   ///</span>
<span id="L57"><span class="lineNum">      57</span>              :   /// Subclasses must implement this to support cloning.</span>
<span id="L58"><span class="lineNum">      58</span>              :   BatchNode&lt;I, O&gt; clone();</span>
<span id="L59"><span class="lineNum">      59</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
