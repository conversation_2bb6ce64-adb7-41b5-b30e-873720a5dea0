<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - node.dart<span style="font-size: 80%;"> (source / <a href="node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">18</td>
            <td class="headerCovTableEntry">18</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/base_node.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// A class that represents a node in a PocketFlow workflow.</span>
<span id="L6"><span class="lineNum">       6</span>              : ///</span>
<span id="L7"><span class="lineNum">       7</span>              : /// A `Node` is an extension of [BaseNode] that adds retry logic to the `exec`</span>
<span id="L8"><span class="lineNum">       8</span>              : /// method. This is useful for operations that might fail intermittently, such</span>
<span id="L9"><span class="lineNum">       9</span>              : /// as network requests.</span>
<span id="L10"><span class="lineNum">      10</span>              : class Node extends BaseNode {</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// Creates a new `Node`.</span>
<span id="L12"><span class="lineNum">      12</span>              :   ///</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// - [maxRetries]: The maximum number of times to retry the `exec` method.</span>
<span id="L14"><span class="lineNum">      14</span>              :   ///   Defaults to `1`.</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// - [wait]: The duration to wait between retries. Defaults to</span>
<span id="L16"><span class="lineNum">      16</span>              :   ///   [Duration.zero].</span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC tlaBgGNC">          13 :   Node({</span></span>
<span id="L18"><span class="lineNum">      18</span>              :     this.maxRetries = 1,</span>
<span id="L19"><span class="lineNum">      19</span>              :     this.wait = Duration.zero,</span>
<span id="L20"><span class="lineNum">      20</span>              :   });</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// The maximum number of times to retry the `exec` method upon failure.</span>
<span id="L23"><span class="lineNum">      23</span>              :   /// A value of 1 means the `exec` method will be attempted once. A value of 2</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// means it will be attempted once, and if it fails, it will be retried once</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// more.</span>
<span id="L26"><span class="lineNum">      26</span>              :   final int maxRetries;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   /// The duration to wait between retries. If `maxRetries` is greater than 1,</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// this is the amount of time the node will pause before re-attempting the</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// `exec` method.</span>
<span id="L31"><span class="lineNum">      31</span>              :   final Duration wait;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   /// A fallback method that is called when the `exec` method fails after all</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// retries have been exhausted.</span>
<span id="L35"><span class="lineNum">      35</span>              :   ///</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// The [prepResult] is the result from the `prep` method, and the [error] is</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// the exception that was caught during the last attempt of the `exec`</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// method.</span>
<span id="L39"><span class="lineNum">      39</span>              :   ///</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// The default implementation re-throws the error. This method can be</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// overridden to provide custom fallback logic, such as returning a</span>
<span id="L42"><span class="lineNum">      42</span>              :   /// default value or gracefully degrading functionality.</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :   Future&lt;dynamic&gt; execFallback(dynamic prepResult, Exception error) async {</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     throw error;</span>
<span id="L45"><span class="lineNum">      45</span>              :   }</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">          11 :   @override</span></span>
<span id="L48"><span class="lineNum">      48</span>              :   /// Executes the node's lifecycle (`prep` -&gt; `exec` -&gt; `post`) with retry</span>
<span id="L49"><span class="lineNum">      49</span>              :   /// logic.</span>
<span id="L50"><span class="lineNum">      50</span>              :   ///</span>
<span id="L51"><span class="lineNum">      51</span>              :   /// If the `exec` method fails, it will be retried up to [maxRetries] times.</span>
<span id="L52"><span class="lineNum">      52</span>              :   /// A [wait] duration can be specified to delay between retries. If all</span>
<span id="L53"><span class="lineNum">      53</span>              :   /// retries fail, the [execFallback] method is called.</span>
<span id="L54"><span class="lineNum">      54</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">          11 :     final prepResult = await prep(shared);</span></span>
<span id="L56"><span class="lineNum">      56</span>              :     dynamic execResult;</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">          23 :     for (var attempt = 0; attempt &lt; maxRetries; attempt++) {</span></span>
<span id="L59"><span class="lineNum">      59</span>              :       try {</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">          11 :         execResult = await exec(prepResult);</span></span>
<span id="L61"><span class="lineNum">      61</span>              :         break; // Success, exit loop</span>
<span id="L62"><span class="lineNum">      62</span>              :       } catch (e) {</span>
<span id="L63"><span class="lineNum">      63</span>              :         // If it's not an Exception, rethrow immediately. The retry logic is</span>
<span id="L64"><span class="lineNum">      64</span>              :         // only for recoverable Exceptions.</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           2 :         if (e is! Exception) {</span></span>
<span id="L66"><span class="lineNum">      66</span>              :           rethrow;</span>
<span id="L67"><span class="lineNum">      67</span>              :         }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :         // If it's the last attempt for a recoverable Exception, use fallback.</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           3 :         if (attempt == maxRetries - 1) {</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :           execResult = await execFallback(prepResult, e);</span></span>
<span id="L72"><span class="lineNum">      72</span>              :           break; // Exit loop after fallback</span>
<span id="L73"><span class="lineNum">      73</span>              :         }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           2 :         if (wait &gt; Duration.zero) {</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           2 :           await Future&lt;void&gt;.delayed(wait);</span></span>
<span id="L77"><span class="lineNum">      77</span>              :         }</span>
<span id="L78"><span class="lineNum">      78</span>              :       }</span>
<span id="L79"><span class="lineNum">      79</span>              :     }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">          11 :     return post(shared, prepResult, execResult);</span></span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L85"><span class="lineNum">      85</span>              :   /// Creates a copy of the node.</span>
<span id="L86"><span class="lineNum">      86</span>              :   Node clone() {</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :     return Node(</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :         maxRetries: maxRetries,</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           1 :         wait: wait,</span></span>
<span id="L90"><span class="lineNum">      90</span>              :       )</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           2 :       ..name = name</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           3 :       ..params = Map.from(params);</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   }</span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
