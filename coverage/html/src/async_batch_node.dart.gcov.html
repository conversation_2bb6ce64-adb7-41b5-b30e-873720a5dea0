<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/async_batch_node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - async_batch_node.dart<span style="font-size: 80%;"> (source / <a href="async_batch_node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">20</td>
            <td class="headerCovTableEntry">20</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:pocketflow/src/node.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// A function type for an asynchronous batch execution block.</span>
<span id="L4"><span class="lineNum">       4</span>              : typedef AsyncBatchExecFunction&lt;I, O&gt; = Future&lt;List&lt;O&gt;&gt; Function(List&lt;I&gt; items);</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// A class for defining nodes that process a batch of items asynchronously.</span>
<span id="L7"><span class="lineNum">       7</span>              : ///</span>
<span id="L8"><span class="lineNum">       8</span>              : /// `AsyncBatchNode` is a convenience class that simplifies the creation of</span>
<span id="L9"><span class="lineNum">       9</span>              : /// nodes that perform an asynchronous batch operation. Instead of creating a</span>
<span id="L10"><span class="lineNum">      10</span>              : /// new class that extends [Node] and implementing the batch logic, you can</span>
<span id="L11"><span class="lineNum">      11</span>              : /// pass an [AsyncBatchExecFunction] directly to the constructor.</span>
<span id="L12"><span class="lineNum">      12</span>              : class AsyncBatchNode&lt;I, O&gt; extends Node {</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// Creates a new `AsyncBatchNode`.</span>
<span id="L14"><span class="lineNum">      14</span>              :   ///</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// - [execFunction]: The asynchronous function to be executed by this node.</span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC tlaBgGNC">           3 :   AsyncBatchNode(AsyncBatchExecFunction&lt;I, O&gt; execFunction)</span></span>
<span id="L17"><span class="lineNum">      17</span>              :     : _execFunction = execFunction;</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   /// The asynchronous function to be executed by this node.</span>
<span id="L20"><span class="lineNum">      20</span>              :   final AsyncBatchExecFunction&lt;I, O&gt; _execFunction;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L23"><span class="lineNum">      23</span>              :   /// Prepares the batch of items for processing.</span>
<span id="L24"><span class="lineNum">      24</span>              :   ///</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// This method retrieves the list of items from the `shared` map or the</span>
<span id="L26"><span class="lineNum">      26</span>              :   /// node's parameters. It expects a parameter named &quot;items&quot; which should be</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// a `List&lt;I&gt;`.</span>
<span id="L28"><span class="lineNum">      28</span>              :   ///</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Throws an [ArgumentError] if the &quot;items&quot; parameter is not provided or is</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// of the wrong type.</span>
<span id="L31"><span class="lineNum">      31</span>              :   Future&lt;List&lt;I&gt;&gt; prep(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           5 :     final items = shared['items'] ?? params['items'];</span></span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :     if (items == null) {</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :       throw ArgumentError('The &quot;items&quot; parameter must be provided.');</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           3 :     if (items is List&lt;I&gt;) {</span></span>
<span id="L39"><span class="lineNum">      39</span>              :       return items;</span>
<span id="L40"><span class="lineNum">      40</span>              :     }</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           1 :     if (items is List) {</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           3 :       if (items.every((item) =&gt; item is I)) {</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :         return items.cast&lt;I&gt;();</span></span>
<span id="L45"><span class="lineNum">      45</span>              :       } else {</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :         throw ArgumentError(</span></span>
<span id="L47"><span class="lineNum">      47</span>              :           'The &quot;items&quot; parameter must be a List where all elements are of '</span>
<span id="L48"><span class="lineNum">      48</span>              :           'type $I.',</span>
<span id="L49"><span class="lineNum">      49</span>              :         );</span>
<span id="L50"><span class="lineNum">      50</span>              :       }</span>
<span id="L51"><span class="lineNum">      51</span>              :     }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :     throw ArgumentError(</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :       'The &quot;items&quot; parameter must be a List, but got ${items.runtimeType}.',</span></span>
<span id="L55"><span class="lineNum">      55</span>              :     );</span>
<span id="L56"><span class="lineNum">      56</span>              :   }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L59"><span class="lineNum">      59</span>              :   /// Executes the asynchronous batch function.</span>
<span id="L60"><span class="lineNum">      60</span>              :   ///</span>
<span id="L61"><span class="lineNum">      61</span>              :   /// This method calls the [_execFunction] that was passed to the constructor.</span>
<span id="L62"><span class="lineNum">      62</span>              :   Future&lt;List&lt;O&gt;&gt; exec(covariant List&lt;I&gt; prepResult) {</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           6 :     return _execFunction(prepResult);</span></span>
<span id="L64"><span class="lineNum">      64</span>              :   }</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span>              :   /// Creates a copy of this [AsyncBatchNode].</span>
<span id="L68"><span class="lineNum">      68</span>              :   AsyncBatchNode&lt;I, O&gt; clone() {</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           6 :     return AsyncBatchNode&lt;I, O&gt;(_execFunction)</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           6 :       ..name = name</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           9 :       ..params = Map.from(params);</span></span>
<span id="L72"><span class="lineNum">      72</span>              :   }</span>
<span id="L73"><span class="lineNum">      73</span>              : </span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L75"><span class="lineNum">      75</span>              :   /// Executes the node's lifecycle and updates the shared state.</span>
<span id="L76"><span class="lineNum">      76</span>              :   ///</span>
<span id="L77"><span class="lineNum">      77</span>              :   /// This method calls the parent `run` method and then stores the result</span>
<span id="L78"><span class="lineNum">      78</span>              :   /// back into the `shared` map under the key &quot;items&quot;. This allows subsequent</span>
<span id="L79"><span class="lineNum">      79</span>              :   /// nodes in the flow to access the processed batch.</span>
<span id="L80"><span class="lineNum">      80</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           3 :     final result = await super.run(shared);</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           3 :     shared['items'] = result;</span></span>
<span id="L83"><span class="lineNum">      83</span>              :     return result;</span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
