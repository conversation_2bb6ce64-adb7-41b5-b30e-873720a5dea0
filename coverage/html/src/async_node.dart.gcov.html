<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/async_node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - async_node.dart<span style="font-size: 80%;"> (source / <a href="async_node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">8</td>
            <td class="headerCovTableEntry">8</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:pocketflow/src/node.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// A function type for an asynchronous execution block.</span>
<span id="L4"><span class="lineNum">       4</span>              : typedef AsyncExecFunction = Future&lt;dynamic&gt; Function(dynamic prepResult);</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// A class for defining nodes with `async`/`await` native methods.</span>
<span id="L7"><span class="lineNum">       7</span>              : ///</span>
<span id="L8"><span class="lineNum">       8</span>              : /// `AsyncNode` is a convenience class that simplifies the creation of nodes</span>
<span id="L9"><span class="lineNum">       9</span>              : /// that perform an asynchronous operation. Instead of creating a new class</span>
<span id="L10"><span class="lineNum">      10</span>              : /// that extends [Node] and overriding the `exec` method, you can pass an</span>
<span id="L11"><span class="lineNum">      11</span>              : /// [AsyncExecFunction] directly to the constructor.</span>
<span id="L12"><span class="lineNum">      12</span>              : class AsyncNode extends Node {</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// Creates a new `AsyncNode`.</span>
<span id="L14"><span class="lineNum">      14</span>              :   ///</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// - [execFunction]: The asynchronous function to be executed by this node.</span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC tlaBgGNC">           4 :   AsyncNode(AsyncExecFunction execFunction) : _execFunction = execFunction;</span></span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   /// The asynchronous function to be executed by this node.</span>
<span id="L19"><span class="lineNum">      19</span>              :   final AsyncExecFunction _execFunction;</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Prepares the data for the `exec` method.</span>
<span id="L23"><span class="lineNum">      23</span>              :   ///</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// This implementation simply returns the [shared] map.</span>
<span id="L25"><span class="lineNum">      25</span>              :   Future&lt;dynamic&gt; prep(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L26"><span class="lineNum">      26</span>              :     return shared;</span>
<span id="L27"><span class="lineNum">      27</span>              :   }</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           4 :   @override</span></span>
<span id="L30"><span class="lineNum">      30</span>              :   /// Executes the asynchronous function.</span>
<span id="L31"><span class="lineNum">      31</span>              :   ///</span>
<span id="L32"><span class="lineNum">      32</span>              :   /// This method calls the [_execFunction] that was passed to the constructor.</span>
<span id="L33"><span class="lineNum">      33</span>              :   Future&lt;dynamic&gt; exec(dynamic prepResult) {</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           8 :     return _execFunction(prepResult);</span></span>
<span id="L35"><span class="lineNum">      35</span>              :   }</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           4 :   @override</span></span>
<span id="L38"><span class="lineNum">      38</span>              :   /// Creates a copy of this [AsyncNode].</span>
<span id="L39"><span class="lineNum">      39</span>              :   AsyncNode clone() {</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           8 :     return AsyncNode(_execFunction)</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           8 :       ..name = name</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">          12 :       ..params = Map.from(params);</span></span>
<span id="L43"><span class="lineNum">      43</span>              :   }</span>
<span id="L44"><span class="lineNum">      44</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
