<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/base_node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - base_node.dart<span style="font-size: 80%;"> (source / <a href="base_node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">22</td>
            <td class="headerCovTableEntry">22</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// A helper class to represent a pending conditional transition.</span>
<span id="L4"><span class="lineNum">       4</span>              : ///</span>
<span id="L5"><span class="lineNum">       5</span>              : /// This class is used to chain nodes conditionally using the `-` and `&gt;&gt;`</span>
<span id="L6"><span class="lineNum">       6</span>              : /// operators. For example:</span>
<span id="L7"><span class="lineNum">       7</span>              : ///</span>
<span id="L8"><span class="lineNum">       8</span>              : /// ```dart</span>
<span id="L9"><span class="lineNum">       9</span>              : /// nodeA - 'success' &gt;&gt; nodeB;</span>
<span id="L10"><span class="lineNum">      10</span>              : /// nodeA - 'failure' &gt;&gt; nodeC;</span>
<span id="L11"><span class="lineNum">      11</span>              : /// ```</span>
<span id="L12"><span class="lineNum">      12</span>              : class ConditionalTransition {</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// Creates a new conditional transition.</span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC tlaBgGNC">           1 :   ConditionalTransition(this.from, this.action);</span></span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   /// The source node of the transition.</span>
<span id="L17"><span class="lineNum">      17</span>              :   final BaseNode from;</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   /// The action that triggers the transition.</span>
<span id="L20"><span class="lineNum">      20</span>              :   final String action;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Chains the transition to the [to] node.</span>
<span id="L23"><span class="lineNum">      23</span>              :   ///</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// This is equivalent to `from.next(to, action: action)`.</span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           1 :   BaseNode operator &gt;&gt;(BaseNode to) {</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           3 :     return from.next(to, action: action);</span></span>
<span id="L27"><span class="lineNum">      27</span>              :   }</span>
<span id="L28"><span class="lineNum">      28</span>              : }</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              : /// An abstract class representing a node in a workflow.</span>
<span id="L31"><span class="lineNum">      31</span>              : ///</span>
<span id="L32"><span class="lineNum">      32</span>              : /// A node is a single step in a workflow. It can be connected to other nodes</span>
<span id="L33"><span class="lineNum">      33</span>              : /// to form a directed acyclic graph (DAG). Each node has a `prep` method for</span>
<span id="L34"><span class="lineNum">      34</span>              : /// pre-processing, an `exec` method for the main execution logic, and a `post`</span>
<span id="L35"><span class="lineNum">      35</span>              : /// method for post-processing.</span>
<span id="L36"><span class="lineNum">      36</span>              : abstract class BaseNode {</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// The unique name of the node.</span>
<span id="L38"><span class="lineNum">      38</span>              :   ///</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// This is used to identify the node in the workflow. If not provided, the</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// runtime type of the node is used.</span>
<span id="L41"><span class="lineNum">      41</span>              :   String? name;</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :   /// The parameters for the node.</span>
<span id="L44"><span class="lineNum">      44</span>              :   ///</span>
<span id="L45"><span class="lineNum">      45</span>              :   /// These parameters can be used to configure the node's behavior. They are</span>
<span id="L46"><span class="lineNum">      46</span>              :   /// accessible within the `prep`, `exec`, and `post` methods.</span>
<span id="L47"><span class="lineNum">      47</span>              :   Map&lt;String, dynamic&gt; params = {};</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span>              :   /// A function for logging messages.</span>
<span id="L50"><span class="lineNum">      50</span>              :   ///</span>
<span id="L51"><span class="lineNum">      51</span>              :   /// This can be overridden to use a different logging mechanism. The default</span>
<span id="L52"><span class="lineNum">      52</span>              :   /// implementation does nothing.</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :   void Function(String) log = (_) {};</span></span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              :   /// The successor nodes.</span>
<span id="L56"><span class="lineNum">      56</span>              :   ///</span>
<span id="L57"><span class="lineNum">      57</span>              :   /// This is a map of action names to successor nodes. When a node finishes</span>
<span id="L58"><span class="lineNum">      58</span>              :   /// execution, it can return an action name to determine which node to</span>
<span id="L59"><span class="lineNum">      59</span>              :   /// execute next.</span>
<span id="L60"><span class="lineNum">      60</span>              :   final Map&lt;String, BaseNode&gt; successors = {};</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              :   /// Defines the next node in the sequence.</span>
<span id="L63"><span class="lineNum">      63</span>              :   ///</span>
<span id="L64"><span class="lineNum">      64</span>              :   /// This method is used to connect nodes in a workflow. The [action] parameter</span>
<span id="L65"><span class="lineNum">      65</span>              :   /// can be used to create conditional transitions.</span>
<span id="L66"><span class="lineNum">      66</span>              :   ///</span>
<span id="L67"><span class="lineNum">      67</span>              :   /// Returns the [node] for chaining.</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           8 :   BaseNode next(BaseNode node, {String action = 'default'}) {</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">          16 :     if (successors.containsKey(action)) {</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           6 :       log(</span></span>
<span id="L71"><span class="lineNum">      71</span>              :         'Warning: Overwriting existing successor for action &quot;$action&quot; on node '</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           6 :         '&quot;${name ?? runtimeType}&quot;.',</span></span>
<span id="L73"><span class="lineNum">      73</span>              :       );</span>
<span id="L74"><span class="lineNum">      74</span>              :     }</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">          16 :     successors[action] = node;</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     return node;</span>
<span id="L77"><span class="lineNum">      77</span>              :   }</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span>              :   /// Chains the current node to the [other] node.</span>
<span id="L80"><span class="lineNum">      80</span>              :   ///</span>
<span id="L81"><span class="lineNum">      81</span>              :   /// This is a shorthand for `next(other)`.</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           1 :   BaseNode operator &gt;&gt;(BaseNode other) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           1 :     return next(other);</span></span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :   /// Creates a conditional transition with the given [action].</span>
<span id="L87"><span class="lineNum">      87</span>              :   ///</span>
<span id="L88"><span class="lineNum">      88</span>              :   /// This is used with the `&gt;&gt;` operator to chain nodes conditionally.</span>
<span id="L89"><span class="lineNum">      89</span>              :   ///</span>
<span id="L90"><span class="lineNum">      90</span>              :   /// ```dart</span>
<span id="L91"><span class="lineNum">      91</span>              :   /// nodeA - 'success' &gt;&gt; nodeB;</span>
<span id="L92"><span class="lineNum">      92</span>              :   /// ```</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :   ConditionalTransition operator -(String action) {</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :     return ConditionalTransition(this, action);</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   }</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span>              :   /// Pre-processing logic before `exec`.</span>
<span id="L98"><span class="lineNum">      98</span>              :   ///</span>
<span id="L99"><span class="lineNum">      99</span>              :   /// This method is called before the `exec` method. It can be used to prepare</span>
<span id="L100"><span class="lineNum">     100</span>              :   /// data for the `exec` method. The [shared] map contains data that is</span>
<span id="L101"><span class="lineNum">     101</span>              :   /// shared across all nodes in the workflow.</span>
<span id="L102"><span class="lineNum">     102</span>              :   ///</span>
<span id="L103"><span class="lineNum">     103</span>              :   /// Returns a value that will be passed to the `exec` method.</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           5 :   Future&lt;dynamic&gt; prep(Map&lt;String, dynamic&gt; shared) async {</span></span>
<span id="L105"><span class="lineNum">     105</span>              :     // Default implementation does nothing.</span>
<span id="L106"><span class="lineNum">     106</span>              :   }</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span>              :   /// The main execution logic for the node.</span>
<span id="L109"><span class="lineNum">     109</span>              :   ///</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// This method is called after the `prep` method. It should contain the main</span>
<span id="L111"><span class="lineNum">     111</span>              :   /// logic for the node. The [prepResult] is the value returned by the `prep`</span>
<span id="L112"><span class="lineNum">     112</span>              :   /// method.</span>
<span id="L113"><span class="lineNum">     113</span>              :   ///</span>
<span id="L114"><span class="lineNum">     114</span>              :   /// Returns a value that will be passed to the `post` method.</span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           4 :   Future&lt;dynamic&gt; exec(dynamic prepResult) async {</span></span>
<span id="L116"><span class="lineNum">     116</span>              :     // Default implementation does nothing.</span>
<span id="L117"><span class="lineNum">     117</span>              :   }</span>
<span id="L118"><span class="lineNum">     118</span>              : </span>
<span id="L119"><span class="lineNum">     119</span>              :   /// Post-processing logic after `exec`.</span>
<span id="L120"><span class="lineNum">     120</span>              :   ///</span>
<span id="L121"><span class="lineNum">     121</span>              :   /// This method is called after the `exec` method. It can be used to process</span>
<span id="L122"><span class="lineNum">     122</span>              :   /// the result of the `exec` method and update the [shared] map. The</span>
<span id="L123"><span class="lineNum">     123</span>              :   /// [prepResult] is the value returned by the `prep` method, and the</span>
<span id="L124"><span class="lineNum">     124</span>              :   /// [execResult] is the value returned by the `exec` method.</span>
<span id="L125"><span class="lineNum">     125</span>              :   ///</span>
<span id="L126"><span class="lineNum">     126</span>              :   /// Returns a value that will be returned by the `run` method.</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">          12 :   Future&lt;dynamic&gt; post(</span></span>
<span id="L128"><span class="lineNum">     128</span>              :     Map&lt;String, dynamic&gt; shared,</span>
<span id="L129"><span class="lineNum">     129</span>              :     dynamic prepResult,</span>
<span id="L130"><span class="lineNum">     130</span>              :     dynamic execResult,</span>
<span id="L131"><span class="lineNum">     131</span>              :   ) async {</span>
<span id="L132"><span class="lineNum">     132</span>              :     // Default implementation returns the execution result.</span>
<span id="L133"><span class="lineNum">     133</span>              :     return execResult;</span>
<span id="L134"><span class="lineNum">     134</span>              :   }</span>
<span id="L135"><span class="lineNum">     135</span>              : </span>
<span id="L136"><span class="lineNum">     136</span>              :   /// Executes the node's lifecycle (`prep` -&gt; `exec` -&gt; `post`).</span>
<span id="L137"><span class="lineNum">     137</span>              :   ///</span>
<span id="L138"><span class="lineNum">     138</span>              :   /// This method is called by the workflow to execute the node. It orchestrates</span>
<span id="L139"><span class="lineNum">     139</span>              :   /// the call to `prep`, `exec`, and `post` methods. It should not be called</span>
<span id="L140"><span class="lineNum">     140</span>              :   /// directly unless for testing purposes.</span>
<span id="L141"><span class="lineNum">     141</span>              :   ///</span>
<span id="L142"><span class="lineNum">     142</span>              :   /// The [shared] map contains data that is shared across all nodes in the</span>
<span id="L143"><span class="lineNum">     143</span>              :   /// workflow.</span>
<span id="L144"><span class="lineNum">     144</span>              :   ///</span>
<span id="L145"><span class="lineNum">     145</span>              :   /// Returns the result of the `post` method.</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaGNC">           2 :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           4 :     if (successors.isNotEmpty) {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaGNC">           2 :       log(</span></span>
<span id="L149"><span class="lineNum">     149</span>              :         'Warning: Calling run() on a node with successors has no effect on '</span>
<span id="L150"><span class="lineNum">     150</span>              :         'flow execution. To execute the entire flow, call run() on the Flow '</span>
<span id="L151"><span class="lineNum">     151</span>              :         'instance instead.',</span>
<span id="L152"><span class="lineNum">     152</span>              :       );</span>
<span id="L153"><span class="lineNum">     153</span>              :     }</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           2 :     final prepResult = await prep(shared);</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaGNC">           2 :     final execResult = await exec(prepResult);</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           2 :     return post(shared, prepResult, execResult);</span></span>
<span id="L157"><span class="lineNum">     157</span>              :   }</span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span>              :   /// Creates a copy of the node.</span>
<span id="L160"><span class="lineNum">     160</span>              :   ///</span>
<span id="L161"><span class="lineNum">     161</span>              :   /// Subclasses should implement this method to support cloning of nodes.</span>
<span id="L162"><span class="lineNum">     162</span>              :   BaseNode clone();</span>
<span id="L163"><span class="lineNum">     163</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
