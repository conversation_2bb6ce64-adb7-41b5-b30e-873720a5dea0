<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/streaming_batch_flow.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - streaming_batch_flow.dart<span style="font-size: 80%;"> (source / <a href="streaming_batch_flow.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">83.3&nbsp;%</td>
            <td class="headerCovTableEntry">12</td>
            <td class="headerCovTableEntry">10</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/async_flow.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:pocketflow/src/base_node.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// A flow that processes a batch of items sequentially through a series of</span>
<span id="L7"><span class="lineNum">       7</span>              : /// nodes.</span>
<span id="L8"><span class="lineNum">       8</span>              : ///</span>
<span id="L9"><span class="lineNum">       9</span>              : /// `StreamingBatchFlow` is designed for scenarios where a collection of items</span>
<span id="L10"><span class="lineNum">      10</span>              : /// needs to be processed in a pipeline fashion. Each node in the flow receives</span>
<span id="L11"><span class="lineNum">      11</span>              : /// the batch of items, performs an operation, and passes the modified batch to</span>
<span id="L12"><span class="lineNum">      12</span>              : /// the next node.</span>
<span id="L13"><span class="lineNum">      13</span>              : ///</span>
<span id="L14"><span class="lineNum">      14</span>              : /// The flow is asynchronous, leveraging Dart's `Future` to handle operations</span>
<span id="L15"><span class="lineNum">      15</span>              : /// that may not complete immediately.</span>
<span id="L16"><span class="lineNum">      16</span>              : class StreamingBatchFlow&lt;TIn, TOut&gt; extends AsyncFlow {</span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Creates an instance of [StreamingBatchFlow].</span>
<span id="L18"><span class="lineNum">      18</span>              :   ///</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// The [nodes] parameter is a list of [BaseNode] instances that make up the</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// flow. The nodes are chained together in the order they are provided.</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC tlaBgGNC">           1 :   StreamingBatchFlow(List&lt;BaseNode&gt; nodes) {</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           1 :     if (nodes.isEmpty) {</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC tlaBgUNC">           0 :       throw ArgumentError('The list of nodes cannot be empty.');</span></span>
<span id="L24"><span class="lineNum">      24</span>              :     }</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC tlaBgGNC">           2 :     start(nodes.first);</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           4 :     for (var i = 0; i &lt; nodes.length - 1; i++) {</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           4 :       nodes[i].next(nodes[i + 1]);</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     }</span>
<span id="L30"><span class="lineNum">      30</span>              :   }</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L33"><span class="lineNum">      33</span>              :   /// Executes the streaming batch flow.</span>
<span id="L34"><span class="lineNum">      34</span>              :   ///</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// This method expects a list of items to be provided in the flow's</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// parameters under the key &quot;items&quot;. It then populates the shared state with</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// this list and executes the flow of nodes.</span>
<span id="L38"><span class="lineNum">      38</span>              :   ///</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// Returns the result of the last node in the flow, which is expected to be</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// the final processed batch of items.</span>
<span id="L41"><span class="lineNum">      41</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L42"><span class="lineNum">      42</span>              :     // Before the flow starts, the initial batch of items is expected to be in</span>
<span id="L43"><span class="lineNum">      43</span>              :     // the 'items' parameter of the flow itself.</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           5 :     if (!params.containsKey('items') || params['items'] is! List) {</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC tlaBgUNC">           0 :       throw ArgumentError(</span></span>
<span id="L46"><span class="lineNum">      46</span>              :         'StreamingBatchFlow requires a list of items under the key &quot;items&quot; in '</span>
<span id="L47"><span class="lineNum">      47</span>              :         'its params.',</span>
<span id="L48"><span class="lineNum">      48</span>              :       );</span>
<span id="L49"><span class="lineNum">      49</span>              :     }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              :     // The initial `shared` storage is populated with the batch of items.</span>
<span id="L52"><span class="lineNum">      52</span>              :     // Each subsequent node in the flow is responsible for reading from and</span>
<span id="L53"><span class="lineNum">      53</span>              :     // writing to the 'items' key in the `shared` map.</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC tlaBgGNC">           1 :     final initialShared = Map&lt;String, dynamic&gt;.from(shared);</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           4 :     initialShared['items'] = List&lt;TIn&gt;.from(params['items'] as List);</span></span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :     // The `super.run()` method executes the flow of nodes. Each node will</span>
<span id="L58"><span class="lineNum">      58</span>              :     // read and write to the `initialShared` map.</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :     return super.run(initialShared);</span></span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
