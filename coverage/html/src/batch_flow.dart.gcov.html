<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/batch_flow.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - batch_flow.dart<span style="font-size: 80%;"> (source / <a href="batch_flow.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">18</td>
            <td class="headerCovTableEntry">18</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/flow.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:pocketflow/src/node.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// A [BatchFlow] is a specialized [Flow] that processes a batch of inputs.</span>
<span id="L7"><span class="lineNum">       7</span>              : ///</span>
<span id="L8"><span class="lineNum">       8</span>              : /// It orchestrates a series of nodes to be executed sequentially for each</span>
<span id="L9"><span class="lineNum">       9</span>              : /// input in a provided list.</span>
<span id="L10"><span class="lineNum">      10</span>              : class BatchFlow&lt;I, O&gt; extends Flow {</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// Creates a new [BatchFlow] with a list of [nodes].</span>
<span id="L12"><span class="lineNum">      12</span>              :   ///</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// The [nodes] are chained together in the order they are provided.</span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC tlaBgGNC">           1 :   BatchFlow(List&lt;Node&gt; nodes) : _nodes = nodes {</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaGNC">           1 :     if (nodes.isEmpty) {</span></span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           1 :       throw ArgumentError('The list of nodes cannot be empty.');</span></span>
<span id="L17"><span class="lineNum">      17</span>              :     }</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :     // Set the start node of the flow</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           2 :     start(nodes.first);</span></span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :     // Chain the rest of the nodes sequentially</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           4 :     for (var i = 0; i &lt; nodes.length - 1; i++) {</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           4 :       nodes[i].next(nodes[i + 1]);</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     }</span>
<span id="L26"><span class="lineNum">      26</span>              :   }</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   /// The list of nodes that make up the flow.</span>
<span id="L29"><span class="lineNum">      29</span>              :   final List&lt;Node&gt; _nodes;</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L32"><span class="lineNum">      32</span>              :   /// Runs the flow for a batch of inputs.</span>
<span id="L33"><span class="lineNum">      33</span>              :   ///</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// This method overrides the parent [Flow.run] method. It expects the</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// `shared` map to contain a list of inputs under the key `'items'`.</span>
<span id="L36"><span class="lineNum">      36</span>              :   ///</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// It iterates over the inputs, and for each one, it executes the entire</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// flow by calling `super.run()`. This ensures that each execution is</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// isolated, as the parent `run` method handles the cloning of nodes.</span>
<span id="L40"><span class="lineNum">      40</span>              :   ///</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// Returns a list of outputs corresponding to each input.</span>
<span id="L42"><span class="lineNum">      42</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           3 :     if (!shared.containsKey('items') || shared['items'] is! List) {</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :       throw ArgumentError(</span></span>
<span id="L45"><span class="lineNum">      45</span>              :         'BatchFlow requires a list of items under the key &quot;items&quot;.',</span>
<span id="L46"><span class="lineNum">      46</span>              :       );</span>
<span id="L47"><span class="lineNum">      47</span>              :     }</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :     final inputs = List&lt;I&gt;.from(shared['items'] as List);</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :     final outputs = &lt;O&gt;[];</span></span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           2 :     for (final input in inputs) {</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           1 :       final singleInputShared = {'value': input};</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :       final result = await super.run(singleInputShared);</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           1 :       outputs.add(result as O);</span></span>
<span id="L55"><span class="lineNum">      55</span>              :     }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :     return outputs;</span>
<span id="L58"><span class="lineNum">      58</span>              :   }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   /// Creates a deep copy of this [BatchFlow].</span>
<span id="L62"><span class="lineNum">      62</span>              :   BatchFlow&lt;I, O&gt; clone() {</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           5 :     final clonedNodes = _nodes.map((node) =&gt; node.clone()).toList();</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           1 :     return BatchFlow&lt;I, O&gt;(clonedNodes);</span></span>
<span id="L65"><span class="lineNum">      65</span>              :   }</span>
<span id="L66"><span class="lineNum">      66</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
