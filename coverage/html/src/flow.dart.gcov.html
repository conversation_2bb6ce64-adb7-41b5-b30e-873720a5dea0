<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/flow.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - flow.dart<span style="font-size: 80%;"> (source / <a href="flow.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">40</td>
            <td class="headerCovTableEntry">40</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/base_node.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// A [Flow] is a specialized [BaseNode] that orchestrates the execution of a</span>
<span id="L6"><span class="lineNum">       6</span>              : /// graph of nodes. It manages the flow of data and control between nodes.</span>
<span id="L7"><span class="lineNum">       7</span>              : class Flow extends BaseNode {</span>
<span id="L8"><span class="lineNum">       8</span>              :   /// Creates a new [Flow].</span>
<span id="L9"><span class="lineNum">       9</span>              :   ///</span>
<span id="L10"><span class="lineNum">      10</span>              :   /// An optional [start] node can be provided to set the entry point of the</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// flow.</span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaGNC tlaBgGNC">           7 :   Flow({BaseNode? start}) : _start = start;</span></span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   /// The starting node of the flow.</span>
<span id="L15"><span class="lineNum">      15</span>              :   BaseNode? _start;</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Sets the starting [node] for the flow. This is the entry point for the</span>
<span id="L18"><span class="lineNum">      18</span>              :   /// execution of the flow.</span>
<span id="L19"><span class="lineNum">      19</span>              :   ///</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Returns the [node] that was set as the start node, allowing for chaining.</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           5 :   BaseNode start(BaseNode node) {</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           5 :     _start = node;</span></span>
<span id="L23"><span class="lineNum">      23</span>              :     return node;</span>
<span id="L24"><span class="lineNum">      24</span>              :   }</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// Clones a node and its successors recursively.</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           5 :   BaseNode? _cloneNode(</span></span>
<span id="L28"><span class="lineNum">      28</span>              :     BaseNode? originalNode,</span>
<span id="L29"><span class="lineNum">      29</span>              :     Map&lt;BaseNode, BaseNode&gt; clonedNodes, [</span>
<span id="L30"><span class="lineNum">      30</span>              :     Map&lt;String, BaseNode&gt;? namedNodes,</span>
<span id="L31"><span class="lineNum">      31</span>              :   ]) {</span>
<span id="L32"><span class="lineNum">      32</span>              :     if (originalNode == null) {</span>
<span id="L33"><span class="lineNum">      33</span>              :       return null;</span>
<span id="L34"><span class="lineNum">      34</span>              :     }</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           5 :     if (clonedNodes.containsKey(originalNode)) {</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           1 :       return clonedNodes[originalNode]!;</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           5 :     final clonedNode = originalNode.clone();</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           5 :     clonedNodes[originalNode] = clonedNode;</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           5 :     if (namedNodes != null &amp;&amp; originalNode.name != null) {</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :       namedNodes[originalNode.name!] = clonedNode;</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     }</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">          15 :     for (final entry in originalNode.successors.entries) {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">          20 :       clonedNode.successors[entry.key] = _cloneNode(</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           5 :         entry.value,</span></span>
<span id="L49"><span class="lineNum">      49</span>              :         clonedNodes,</span>
<span id="L50"><span class="lineNum">      50</span>              :         namedNodes,</span>
<span id="L51"><span class="lineNum">      51</span>              :       )!;</span>
<span id="L52"><span class="lineNum">      52</span>              :     }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              :     return clonedNode;</span>
<span id="L55"><span class="lineNum">      55</span>              :   }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           5 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   /// Executes the flow.</span>
<span id="L59"><span class="lineNum">      59</span>              :   ///</span>
<span id="L60"><span class="lineNum">      60</span>              :   /// This method clones the entire flow, including all nodes, to ensure that</span>
<span id="L61"><span class="lineNum">      61</span>              :   /// each execution is isolated. It then traverses the graph, executing each</span>
<span id="L62"><span class="lineNum">      62</span>              :   /// node in sequence.</span>
<span id="L63"><span class="lineNum">      63</span>              :   ///</span>
<span id="L64"><span class="lineNum">      64</span>              :   /// The [shared] map is passed to each node, allowing them to share data.</span>
<span id="L65"><span class="lineNum">      65</span>              :   ///</span>
<span id="L66"><span class="lineNum">      66</span>              :   /// Returns the result of the last executed node.</span>
<span id="L67"><span class="lineNum">      67</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           5 :     if (_start == null) {</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :       throw StateError('The start node has not been set.');</span></span>
<span id="L70"><span class="lineNum">      70</span>              :     }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           5 :     final clonedNodes = &lt;BaseNode, BaseNode&gt;{};</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           5 :     final namedNodes = &lt;String, BaseNode&gt;{};</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">          10 :     final clonedStart = _cloneNode(_start, clonedNodes, namedNodes);</span></span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              :     final nodeParams =</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           5 :         shared['__node_params__'] as Map&lt;String, Map&lt;String, dynamic&gt;&gt;?;</span></span>
<span id="L78"><span class="lineNum">      78</span>              :     if (nodeParams != null) {</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           2 :       for (final entry in nodeParams.entries) {</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           1 :         final nodeName = entry.key;</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :         final params = entry.value;</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           1 :         if (namedNodes.containsKey(nodeName)) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           3 :           namedNodes[nodeName]!.params.addAll(params);</span></span>
<span id="L84"><span class="lineNum">      84</span>              :         }</span>
<span id="L85"><span class="lineNum">      85</span>              :       }</span>
<span id="L86"><span class="lineNum">      86</span>              :     }</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span>              :     var currentNode = clonedStart;</span>
<span id="L89"><span class="lineNum">      89</span>              :     dynamic lastResult;</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span>              :     while (currentNode != null) {</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           5 :       lastResult = await currentNode.run(shared);</span></span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           5 :       if (lastResult is String &amp;&amp;</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           2 :           currentNode.successors.containsKey(lastResult)) {</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           2 :         currentNode = currentNode.successors[lastResult];</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">          10 :       } else if (currentNode.successors.containsKey('default')) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">          10 :         currentNode = currentNode.successors['default'];</span></span>
<span id="L99"><span class="lineNum">      99</span>              :       } else {</span>
<span id="L100"><span class="lineNum">     100</span>              :         currentNode = null;</span>
<span id="L101"><span class="lineNum">     101</span>              :       }</span>
<span id="L102"><span class="lineNum">     102</span>              :     }</span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span>              :     return lastResult;</span>
<span id="L105"><span class="lineNum">     105</span>              :   }</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span>              :   /// Creates a deep copy of this [Flow].</span>
<span id="L108"><span class="lineNum">     108</span>              :   ///</span>
<span id="L109"><span class="lineNum">     109</span>              :   /// Subclasses can override this method to create a copy of the correct type,</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// but they should call `super.clone()` to ensure the base properties are</span>
<span id="L111"><span class="lineNum">     111</span>              :   /// copied.</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           2 :   T copy&lt;T extends Flow&gt;([T Function()? factory]) {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaGNC">           2 :     final clonedFlow = ((factory != null ? factory() : Flow()) as T)</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           4 :       ..name = name</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           6 :       ..params = Map.from(params);</span></span>
<span id="L116"><span class="lineNum">     116</span>              : </span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           2 :     if (_start != null) {</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           2 :       final clonedNodes = &lt;BaseNode, BaseNode&gt;{};</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           6 :       clonedFlow._start = _cloneNode(_start, clonedNodes);</span></span>
<span id="L120"><span class="lineNum">     120</span>              :     }</span>
<span id="L121"><span class="lineNum">     121</span>              : </span>
<span id="L122"><span class="lineNum">     122</span>              :     return clonedFlow;</span>
<span id="L123"><span class="lineNum">     123</span>              :   }</span>
<span id="L124"><span class="lineNum">     124</span>              : </span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L126"><span class="lineNum">     126</span>              :   /// Creates a deep copy of this [Flow].</span>
<span id="L127"><span class="lineNum">     127</span>              :   Flow clone() {</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaGNC">           1 :     return copy();</span></span>
<span id="L129"><span class="lineNum">     129</span>              :   }</span>
<span id="L130"><span class="lineNum">     130</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
