<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/async_parallel_batch_node.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - async_parallel_batch_node.dart<span style="font-size: 80%;"> (source / <a href="async_parallel_batch_node.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">24</td>
            <td class="headerCovTableEntry">24</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/node.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// A function type for an asynchronous, parallel batch item execution block.</span>
<span id="L6"><span class="lineNum">       6</span>              : typedef AsyncParallelBatchItemExecFunction&lt;I, O&gt; = Future&lt;O&gt; Function(I item);</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : /// A class for defining nodes that process a batch of items asynchronously</span>
<span id="L9"><span class="lineNum">       9</span>              : /// and in parallel.</span>
<span id="L10"><span class="lineNum">      10</span>              : ///</span>
<span id="L11"><span class="lineNum">      11</span>              : /// `AsyncParallelBatchNode` is a convenience class that simplifies the creation</span>
<span id="L12"><span class="lineNum">      12</span>              : /// of nodes that perform an asynchronous, parallel batch operation. You provide</span>
<span id="L13"><span class="lineNum">      13</span>              : /// a function that processes a single item, and the node will apply this</span>
<span id="L14"><span class="lineNum">      14</span>              : /// function to all items in the input batch concurrently using `Future.wait`.</span>
<span id="L15"><span class="lineNum">      15</span>              : class AsyncParallelBatchNode&lt;I, O&gt; extends Node {</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// Creates a new `AsyncParallelBatchNode`.</span>
<span id="L17"><span class="lineNum">      17</span>              :   ///</span>
<span id="L18"><span class="lineNum">      18</span>              :   /// - [execFunction]: The asynchronous function to be executed for each</span>
<span id="L19"><span class="lineNum">      19</span>              :   ///   item in the batch.</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC tlaBgGNC">           1 :   AsyncParallelBatchNode(AsyncParallelBatchItemExecFunction&lt;I, O&gt; execFunction)</span></span>
<span id="L21"><span class="lineNum">      21</span>              :     : _execFunction = execFunction;</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              :   /// The asynchronous function to be executed for each item in the batch.</span>
<span id="L24"><span class="lineNum">      24</span>              :   final AsyncParallelBatchItemExecFunction&lt;I, O&gt; _execFunction;</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// A convenience method to execute the node directly with a list of items.</span>
<span id="L27"><span class="lineNum">      27</span>              :   ///</span>
<span id="L28"><span class="lineNum">      28</span>              :   /// This is primarily for ease of use and testing. It sets the [items] in</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// the node's parameters and calls the `run` method.</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :   Future&lt;List&lt;O&gt;&gt; call(List&lt;I&gt; items) async {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           2 :     params['items'] = items;</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :     final result = await run(params);</span></span>
<span id="L33"><span class="lineNum">      33</span>              :     // The result from `run` is dynamic, so we cast it to the expected type.</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :     if (result is List) {</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :       return result.cast&lt;O&gt;();</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     }</span>
<span id="L37"><span class="lineNum">      37</span>              :     // This path should ideally not be reached if `exec` is correct.</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           1 :     return [];</span></span>
<span id="L39"><span class="lineNum">      39</span>              :   }</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L42"><span class="lineNum">      42</span>              :   /// Prepares the batch of items for processing.</span>
<span id="L43"><span class="lineNum">      43</span>              :   ///</span>
<span id="L44"><span class="lineNum">      44</span>              :   /// This method retrieves the list of items from the node's parameters. It</span>
<span id="L45"><span class="lineNum">      45</span>              :   /// expects a parameter named &quot;items&quot; which should be a `List&lt;I&gt;`.</span>
<span id="L46"><span class="lineNum">      46</span>              :   ///</span>
<span id="L47"><span class="lineNum">      47</span>              :   /// Throws an [ArgumentError] if the &quot;items&quot; parameter is not provided or is</span>
<span id="L48"><span class="lineNum">      48</span>              :   /// of the wrong type.</span>
<span id="L49"><span class="lineNum">      49</span>              :   Future&lt;List&lt;I&gt;&gt; prep(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           2 :     final items = params['items'];</span></span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :     if (items == null) {</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :       throw ArgumentError('The &quot;items&quot; parameter must be provided.');</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     }</span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           1 :     if (items is List&lt;I&gt;) {</span></span>
<span id="L57"><span class="lineNum">      57</span>              :       return items;</span>
<span id="L58"><span class="lineNum">      58</span>              :     }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           1 :     if (items is List) {</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           3 :       if (items.every((item) =&gt; item is I)) {</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           1 :         return items.cast&lt;I&gt;();</span></span>
<span id="L63"><span class="lineNum">      63</span>              :       } else {</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           2 :         throw ArgumentError(</span></span>
<span id="L65"><span class="lineNum">      65</span>              :           'The &quot;items&quot; parameter must be a List where all elements are of '</span>
<span id="L66"><span class="lineNum">      66</span>              :           'type $I.',</span>
<span id="L67"><span class="lineNum">      67</span>              :         );</span>
<span id="L68"><span class="lineNum">      68</span>              :       }</span>
<span id="L69"><span class="lineNum">      69</span>              :     }</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :     throw ArgumentError(</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           2 :       'The &quot;items&quot; parameter must be a List, but got ${items.runtimeType}.',</span></span>
<span id="L73"><span class="lineNum">      73</span>              :     );</span>
<span id="L74"><span class="lineNum">      74</span>              :   }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L77"><span class="lineNum">      77</span>              :   /// Executes the batch processing in parallel.</span>
<span id="L78"><span class="lineNum">      78</span>              :   ///</span>
<span id="L79"><span class="lineNum">      79</span>              :   /// This method applies the [_execFunction] to each item in the [prepResult]</span>
<span id="L80"><span class="lineNum">      80</span>              :   /// list and waits for all the resulting futures to complete using</span>
<span id="L81"><span class="lineNum">      81</span>              :   /// `Future.wait`.</span>
<span id="L82"><span class="lineNum">      82</span>              :   Future&lt;List&lt;O&gt;&gt; exec(covariant List&lt;I&gt; prepResult) {</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           3 :     final futures = prepResult.map(_execFunction).toList();</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :     return Future.wait(futures);</span></span>
<span id="L85"><span class="lineNum">      85</span>              :   }</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L88"><span class="lineNum">      88</span>              :   /// Creates a copy of this [AsyncParallelBatchNode].</span>
<span id="L89"><span class="lineNum">      89</span>              :   AsyncParallelBatchNode&lt;I, O&gt; clone() {</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           2 :     return AsyncParallelBatchNode&lt;I, O&gt;(_execFunction)</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           2 :       ..name = name</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           3 :       ..params = Map.from(params);</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   }</span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
