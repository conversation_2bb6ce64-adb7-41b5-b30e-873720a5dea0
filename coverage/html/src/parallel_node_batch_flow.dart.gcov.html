<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - src/parallel_node_batch_flow.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html">top level</a> - <a href="index.html">src</a> - parallel_node_batch_flow.dart<span style="font-size: 80%;"> (source / <a href="parallel_node_batch_flow.dart.func-c.html">functions</a>)</span></td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">18</td>
            <td class="headerCovTableEntry">18</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-09-29 02:35:49</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:pocketflow/src/async_flow.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:pocketflow/src/base_node.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// A flow that processes a batch of items by executing a set of nodes in</span>
<span id="L7"><span class="lineNum">       7</span>              : /// parallel for each item.</span>
<span id="L8"><span class="lineNum">       8</span>              : ///</span>
<span id="L9"><span class="lineNum">       9</span>              : /// `ParallelNodeBatchFlow` is useful for scenarios where multiple independent</span>
<span id="L10"><span class="lineNum">      10</span>              : /// operations need to be performed on each item in a batch. For each item, the</span>
<span id="L11"><span class="lineNum">      11</span>              : /// flow triggers all nodes concurrently and waits for them to complete.</span>
<span id="L12"><span class="lineNum">      12</span>              : class ParallelNodeBatchFlow&lt;TIn, TOut&gt; extends AsyncFlow {</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// Creates an instance of [ParallelNodeBatchFlow].</span>
<span id="L14"><span class="lineNum">      14</span>              :   ///</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// The [nodes] parameter is a list of [BaseNode] instances that will be</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// executed in parallel for each item in the batch.</span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC tlaBgGNC">           1 :   ParallelNodeBatchFlow(List&lt;BaseNode&gt; nodes) : _nodes = nodes {</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           1 :     if (nodes.isEmpty) {</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           1 :       throw ArgumentError('The list of nodes cannot be empty.');</span></span>
<span id="L20"><span class="lineNum">      20</span>              :     }</span>
<span id="L21"><span class="lineNum">      21</span>              :   }</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              :   /// The list of nodes to be executed in parallel for each item.</span>
<span id="L24"><span class="lineNum">      24</span>              :   final List&lt;BaseNode&gt; _nodes;</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// Executes the flow with a given list of [items].</span>
<span id="L27"><span class="lineNum">      27</span>              :   ///</span>
<span id="L28"><span class="lineNum">      28</span>              :   /// This is a convenience method that calls the [run] method with the</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// provided [items].</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :   Future&lt;dynamic&gt; call(List&lt;TIn&gt; items) async {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           2 :     return run({'input': items});</span></span>
<span id="L32"><span class="lineNum">      32</span>              :   }</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L35"><span class="lineNum">      35</span>              :   /// Executes the parallel, asynchronous batch flow.</span>
<span id="L36"><span class="lineNum">      36</span>              :   ///</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// This method expects a list of items under the key 'input' in the [shared]</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// map. It then iterates over each item and, for each item, executes all the</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// nodes in parallel.</span>
<span id="L40"><span class="lineNum">      40</span>              :   ///</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// Returns a `Future&lt;List&lt;List&lt;dynamic&gt;&gt;&gt;`, where the outer list corresponds</span>
<span id="L42"><span class="lineNum">      42</span>              :   /// to the input items and the inner list contains the results from each node</span>
<span id="L43"><span class="lineNum">      43</span>              :   /// for a given item.</span>
<span id="L44"><span class="lineNum">      44</span>              :   Future&lt;dynamic&gt; run(Map&lt;String, dynamic&gt; shared) async {</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           3 :     if (!shared.containsKey('input') || shared['input'] is! List) {</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :       throw ArgumentError(</span></span>
<span id="L47"><span class="lineNum">      47</span>              :         'ParallelNodeBatchFlow requires a list of items under the key '</span>
<span id="L48"><span class="lineNum">      48</span>              :         '&quot;input&quot; in the shared context. Use the call() method to provide the '</span>
<span id="L49"><span class="lineNum">      49</span>              :         'input list.',</span>
<span id="L50"><span class="lineNum">      50</span>              :       );</span>
<span id="L51"><span class="lineNum">      51</span>              :     }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :     final items = List&lt;TIn&gt;.from(shared['input'] as List);</span></span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           2 :     final batchFutures = items.map((item) {</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           3 :       final nodeFutures = _nodes.map((node) {</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :         final clonedNode = node.clone();</span></span>
<span id="L58"><span class="lineNum">      58</span>              :         // Each node runs with the same item from the batch as input.</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :         return clonedNode.run({'input': item});</span></span>
<span id="L60"><span class="lineNum">      60</span>              :       });</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :       return Future.wait(nodeFutures);</span></span>
<span id="L62"><span class="lineNum">      62</span>              :     });</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           1 :     return Future.wait(batchFutures);</span></span>
<span id="L65"><span class="lineNum">      65</span>              :   }</span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   /// Creates a deep copy of this [ParallelNodeBatchFlow].</span>
<span id="L69"><span class="lineNum">      69</span>              :   ParallelNodeBatchFlow&lt;TIn, TOut&gt; clone() {</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           5 :     final clonedNodes = _nodes.map((node) =&gt; node.clone()).toList();</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :     return ParallelNodeBatchFlow&lt;TIn, TOut&gt;(clonedNodes);</span></span>
<span id="L72"><span class="lineNum">      72</span>              :   }</span>
<span id="L73"><span class="lineNum">      73</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.0-1</a></td></tr>
          </table>
          <br>

</body>
</html>
